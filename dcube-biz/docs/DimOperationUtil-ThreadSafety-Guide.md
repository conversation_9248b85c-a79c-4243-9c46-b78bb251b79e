# DimOperationUtil 线程安全使用指南

## 概述

`DimOperationUtil` 已经过全面的线程安全改造，现在支持高并发环境下的安全执行。本文档详细说明了线程安全的实现方式和最佳使用实践。

## 线程安全特性

### 1. 原子操作
- **执行计数器**: 使用 `AtomicLong` 替代普通 `long`，确保计数操作的原子性
- **缓存操作**: 使用 Caffeine 缓存，天然支持并发访问

### 2. 读写锁机制
- **维护锁**: 使用 `ReentrantReadWriteLock` 实现读写分离
- **并发执行**: 多个线程可以同时执行表达式计算（读锁）
- **维护操作**: 缓存维护时获取写锁，确保数据一致性

### 3. 线程安全的缓存
- **后缀表达式缓存**: 线程安全的 Caffeine 缓存
- **优化表达式缓存**: 支持并发读写
- **符号缓存**: 使用 `ConcurrentHashMap`

## 使用示例

### 基本并发执行

```java
// 创建表达式
List<DimOperationDetailDTO> expression = createExpression();
Map<Long, BigDecimal> variables = createVariables();

// 多线程安全执行
ExecutorService executor = Executors.newFixedThreadPool(10);
List<Future<BigDecimal>> futures = new ArrayList<>();

for (int i = 0; i < 100; i++) {
    Future<BigDecimal> future = executor.submit(() -> {
        ExecutionResult result = DimOperationUtil.executeExpressionWithOptimization(
            expression, variables,
            TraceConfig.disabled(),
            OptimizationConfig.basic()
        );
        return result.getResult();
    });
    futures.add(future);
}

// 收集结果
for (Future<BigDecimal> future : futures) {
    BigDecimal result = future.get();
    // 处理结果
}
```

### 批量并发计算

```java
// 准备批量数据
List<List<DimOperationDetailDTO>> expressions = prepareExpressions();
List<Map<Long, BigDecimal>> variableMaps = prepareVariables();

// 并发执行批量计算
List<ExecutionResult> results = DimOperationUtil.executeExpressionsConcurrently(
    expressions, variableMaps,
    TraceConfig.info(),
    OptimizationConfig.basic()
);

// 处理结果
for (ExecutionResult result : results) {
    System.out.println("结果: " + result.getResult());
    System.out.println("执行时间: " + result.getExecutionTimeMs() + "ms");
}
```

### 缓存性能监控

```java
// 获取缓存性能报告
CachePerformanceReport report = DimOperationUtil.getCachePerformanceReport();
System.out.println(report);

// 检查性能是否良好
if (!report.isPerformanceGood()) {
    // 执行智能调优
    DimOperationUtil.performIntelligentCacheTuning();
}
```

### 内存健康监控

```java
// 检查内存健康状态
MemoryHealthReport healthReport = DimOperationUtil.checkMemoryHealth();
System.out.println(healthReport);

// 根据健康状态采取行动
switch (healthReport.getStatus()) {
    case CRITICAL:
        DimOperationUtil.clearCache();
        break;
    case WARNING:
        DimOperationUtil.performMaintenance();
        break;
    case HEALTHY:
        // 正常运行
        break;
}
```

## 性能优化建议

### 1. 缓存策略优化
- **表达式复用**: 尽量复用相同的表达式结构以提高缓存命中率
- **变量映射**: 保持变量ID的一致性
- **批量处理**: 使用批量并发执行减少单次调用开销

### 2. 并发配置
- **线程池大小**: 建议设置为 CPU 核心数的 1-2 倍
- **批量大小**: 根据内存情况调整批量处理的大小
- **超时设置**: 为长时间运行的计算设置合理的超时时间

### 3. 内存管理
- **定期监控**: 定期检查内存健康状态
- **主动维护**: 在低峰期主动执行缓存维护
- **资源清理**: 及时清理不再使用的 ExecutionContext

## 最佳实践

### 1. 错误处理
```java
try {
    ExecutionResult result = DimOperationUtil.executeExpressionWithOptimization(
        expression, variables, traceConfig, optimizationConfig
    );
    // 处理成功结果
} catch (IllegalArgumentException e) {
    // 处理参数错误
    log.error("表达式参数错误", e);
} catch (Exception e) {
    // 处理其他异常
    log.error("表达式执行失败", e);
}
```

### 2. 资源管理
```java
// 在应用关闭时清理资源
@PreDestroy
public void cleanup() {
    DimOperationUtil.clearCache();
    DimOperationUtil.resetExecutionStats();
}
```

### 3. 监控和调优
```java
// 定期执行性能调优
@Scheduled(fixedRate = 300000) // 每5分钟
public void performCacheTuning() {
    DimOperationUtil.performIntelligentCacheTuning();
}

// 定期检查线程安全状态
@Scheduled(fixedRate = 600000) // 每10分钟
public void checkThreadSafety() {
    String report = DimOperationUtil.getThreadSafetyReport();
    log.info("线程安全状态: {}", report);
}
```

## 性能指标

### 缓存命中率目标
- **后缀表达式缓存**: > 70%
- **优化表达式缓存**: > 60%
- **整体性能**: 良好

### 并发性能
- **线程安全**: 100% 保证
- **并发加速比**: 接近线程数（理想情况）
- **内存使用**: 稳定，无泄漏

### 维护频率
- **自动维护**: 每1000次执行或5分钟
- **手动维护**: 根据内存状态触发
- **缓存清理**: 根据命中率自动调整

## 故障排除

### 常见问题

1. **缓存命中率低**
   - 检查表达式是否频繁变化
   - 验证变量ID的一致性
   - 考虑调整缓存大小

2. **内存使用过高**
   - 检查是否有内存泄漏
   - 调整缓存过期时间
   - 增加维护频率

3. **并发性能不佳**
   - 检查线程池配置
   - 验证是否存在锁竞争
   - 优化批量处理大小

### 调试工具

```java
// 获取详细的执行统计
String stats = DimOperationUtil.getExecutionStats();
System.out.println(stats);

// 获取详细的缓存统计
String cacheStats = DimOperationUtil.getDetailedCacheStats();
System.out.println(cacheStats);

// 获取内存使用情况
String memoryUsage = DimOperationUtil.getMemoryUsage();
System.out.println(memoryUsage);
```

## 版本兼容性

- **向后兼容**: 所有原有的 API 保持兼容
- **新增功能**: 线程安全和并发支持
- **性能提升**: 缓存优化和内存管理改进
- **监控增强**: 新增性能监控和健康检查功能
